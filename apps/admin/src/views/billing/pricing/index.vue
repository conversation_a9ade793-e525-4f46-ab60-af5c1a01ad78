<script setup lang="ts">
import type { StdCurdConfig } from '@billing/curd'
import type { PricingRule } from '@/api/pricing_rule'
import { EasyCurd } from '@billing/curd'
import { pricingRuleApi } from '@/api/pricing_rule'
import { FORM_OPTIONS, MODULE_NAMES, SERVICE_MODULE } from '@/constants'

// 配置 EasyCurd
const config: StdCurdConfig<PricingRule> = {
  api: pricingRuleApi,
  title: '价格规则管理',
  primaryKey: 'id',

  // 表单字段配置 - 使用字段复用功能，自动生成表格列
  formFields: [
    {
      key: 'module',
      label: '服务类型',
      type: 'select',
      required: true,
      options: FORM_OPTIONS.SERVICE_MODULES,
    },
    {
      key: 'model_name',
      label: '模型名称',
      type: 'text',
      required: false,
      placeholder: '留空表示通用规则',
    },
    {
      key: 'unit_price',
      label: '单价',
      type: 'number',
      required: true,
      rules: [
        { required: true, message: '单价是必填项' },
        { min: 0, message: '单价不能为负数' },
      ],
    },
    {
      key: 'currency',
      label: '货币',
      type: 'select',
      required: false,
      defaultValue: 'CNY',
      options: FORM_OPTIONS.CURRENCY,
    },
    {
      key: 'unit',
      label: '计费单位',
      type: 'select',
      required: false,
      defaultValue: 'token',
      options: [
        { label: 'Token', value: 'token' },
        { label: '字符', value: 'character' },
        { label: '秒', value: 'second' },
        { label: '请求', value: 'request' },
      ],
    },
    {
      key: 'base_unit',
      label: '基数单位',
      type: 'select',
      required: false,
      defaultValue: 1,
      options: [
        { label: '1（按个计费）', value: 1 },
        { label: '1,000（按千计费）', value: 1000 },
        { label: '10,000（按万计费）', value: 10000 },
        { label: '100,000（按十万计费）', value: 100000 },
        { label: '1,000,000（按百万计费）', value: 1000000 },
      ],
    },

    {
      key: 'priority',
      label: '优先级',
      type: 'select',
      required: false,
      defaultValue: 1,
      options: [
        { label: '高', value: 1 },
        { label: '中', value: 2 },
        { label: '低', value: 3 },
      ],
    },
    {
      key: 'is_active',
      label: '启用状态',
      type: 'switch',
      defaultValue: true,
    },
    {
      key: 'description',
      label: '备注',
      type: 'textarea',
      required: false,
      placeholder: '规则备注（可选）',
      col: 2, // 占据2列宽度
    },
  ],

  // 功能开关
  features: {
    create: true,
    edit: true,
    delete: true,
    batchDelete: true,
    search: true,
  },

  // 确认消息
  confirmMessages: {
    delete: '确定要删除这条价格规则吗？删除后将无法恢复！',
    batchDelete: '确定要删除选中的价格规则吗？此操作不可撤销！',
  },

  // 成功消息
  successMessages: {
    create: '价格规则创建成功！',
    update: '价格规则更新成功！',
    delete: '价格规则删除成功！',
    batchDelete: '批量删除完成！',
  },

  // 钩子函数
  hooks: {
    // 创建前处理
    beforeCreate: async (data) => {
      console.log('创建价格规则:', data)
      return data
    },

    // 更新前处理
    beforeUpdate: async (id, data) => {
      console.log('更新价格规则:', id, data)
      return data
    },

    // 删除前确认
    beforeDelete: async (id) => {
      console.log('删除价格规则:', id)
    },

    // 错误处理
    onError: async (error, operation, context) => {
      console.error(`操作 ${operation} 失败:`, error, context)
    },
  },
}

// 获取单位名称
function getUnitName(unit: string) {
  const unitNames = {
    token: 'Token',
    character: '字符',
    second: '秒',
    request: '请求',
  }
  return unitNames[unit as keyof typeof unitNames] || unit
}

// 格式化价格显示
function formatPrice(price: number, currency: string) {
  const symbol = currency === 'USD' ? '$' : '¥'
  // 移除末尾的0，最多保留6位小数
  return `${symbol}${Number.parseFloat(price.toFixed(6))}`
}

// 获取单位显示名称
function getUnitDisplayName(baseUnit: number, unit: string) {
  if (!baseUnit || baseUnit <= 1) {
    return getUnitName(unit)
  }

  let prefix = ''
  if (baseUnit >= 1000000) {
    prefix = '百万'
  }
  else if (baseUnit >= 10000) {
    prefix = '万'
  }
  else if (baseUnit >= 1000) {
    prefix = '千'
  }
  else {
    prefix = baseUnit.toString()
  }

  return prefix + getUnitName(unit)
}
</script>

<template>
  <div class="space-y-6">
    <!-- 页面标题和描述 -->
    <div>
      <h1 class="text-2xl font-semibold text-gray-900">
        计费规则配置
      </h1>
    </div>

    <!-- 使用 EasyCurd 组件 -->
    <EasyCurd :config="config">
      <!-- 自定义服务类型列显示 -->
      <template #cell-module="{ value }">
        <span
          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
          :class="{
            'bg-blue-100 text-blue-800': value === SERVICE_MODULE.LLM,
            'bg-green-100 text-green-800': value === SERVICE_MODULE.TTS,
            'bg-purple-100 text-purple-800': value === SERVICE_MODULE.ASR,
          }"
        >
          {{ MODULE_NAMES[value] }}
        </span>
      </template>

      <!-- 自定义模型名称列显示 -->
      <template #cell-model_name="{ value, row }">
        <div>
          <div class="text-sm font-medium text-gray-900">
            {{ value || '通用规则' }}
          </div>
          <div
            v-if="row.description"
            class="text-xs text-gray-500 truncate max-w-xs"
            :title="row.description"
          >
            {{ row.description }}
          </div>
        </div>
      </template>

      <!-- 自定义单价列显示 -->
      <template #cell-unit_price="{ value, row }">
        <div>
          <div class="text-sm font-medium text-gray-900">
            {{ formatPrice(value, row.currency) }}
          </div>
          <div class="text-xs text-gray-500">
            每{{ getUnitDisplayName(row.base_unit, row.unit) }}
          </div>
        </div>
      </template>
      <!-- 自定义优先级列显示 -->
      <template #cell-priority="{ value }">
        <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800">
          {{ value }}
        </span>
      </template>

      <!-- 自定义状态列显示 -->
      <template #cell-is_active="{ value }">
        <div class="flex items-center">
          <div
            class="w-2 h-2 rounded-full mr-2"
            :class="value ? 'bg-green-400' : 'bg-red-400'"
          />
          <span
            class="text-xs font-medium"
            :class="value ? 'text-green-800' : 'text-red-800'"
          >
            {{ value ? '启用' : '禁用' }}
          </span>
        </div>
      </template>
    </EasyCurd>
  </div>
</template>
