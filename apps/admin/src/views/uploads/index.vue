<script setup lang="ts">
import type { <PERSON>urd<PERSON><PERSON>, StdCurdConfig } from '@billing/curd'

import { EasyCurd } from '@billing/curd'
import { Avatar, AvatarFallback, AvatarImage, Badge, Button } from '@billing/ui'
import { Archive, Download, Eye, FileText, Image, Music, Video } from 'lucide-vue-next'
import { formatDate, formatFileSize } from '@/utils/format'
import { getFileTypeBadgeVariant } from '@/utils/business'

// 定义文件接口类型
interface UploadFile {
  id: string
  filename: string
  originalName: string
  fileType: string
  fileSize: number
  mimeType: string
  url: string
  userId: string
  user?: {
    id: string
    name: string
    email: string
    avatar?: string
  }
  createdAt: string
}

// 模拟文件API（实际项目中应该从真实API获取）
const mockFileApi: CurdApi<UploadFile> = {
  async getList(params) {
    // TODO: 实现真实的文件列表API
    await new Promise(resolve => setTimeout(resolve, 500))
    return {
      data: [
        {
          id: '1',
          filename: 'document-2024-01-15.pdf',
          originalName: '用户手册.pdf',
          fileType: 'pdf',
          fileSize: 2048576,
          mimeType: 'application/pdf',
          url: '/uploads/documents/document-2024-01-15.pdf',
          userId: 'user1',
          user: {
            id: 'user1',
            name: '张三',
            email: '<EMAIL>',
          },
          createdAt: '2024-01-15T10:30:00Z',
        },
        {
          id: '2',
          filename: 'avatar-2024-01-14.jpg',
          originalName: '头像.jpg',
          fileType: 'jpg',
          fileSize: 512000,
          mimeType: 'image/jpeg',
          url: '/uploads/images/avatar-2024-01-14.jpg',
          userId: 'user2',
          user: {
            id: 'user2',
            name: '李四',
            email: '<EMAIL>',
          },
          createdAt: '2024-01-14T15:20:00Z',
        },
      ],
      pagination: {
        current_page: 1,
        per_page: 20,
        total_pages: 1,
        total: 2,
      },
    }
  },

  async getItem(id) {
    // TODO: 实现真实的获取单个文件API
    throw new Error('Not implemented')
  },

  async createItem(data) {
    // TODO: 实现真实的文件上传API
    throw new Error('Not implemented')
  },

  async updateItem(id, data) {
    // TODO: 实现真实的文件更新API
    throw new Error('Not implemented')
  },

  async deleteItem(id) {
    // TODO: 实现真实的文件删除API
    console.log('删除文件:', id)
    // 返回一个模拟的文件对象以满足类型要求
    return {
      id: id.toString(),
      filename: 'deleted',
      originalName: 'deleted',
      fileType: 'deleted',
      fileSize: 0,
      mimeType: 'deleted',
      url: 'deleted',
      userId: 'deleted',
      createdAt: new Date().toISOString(),
    }
  },

  async restoreItem(id) {
    // TODO: 实现真实的文件恢复API
    throw new Error('Not implemented')
  },
}

// EasyCurd 配置
const config: StdCurdConfig<UploadFile> = {
  api: mockFileApi,
  title: '文件',

  // 只显示列表，禁用创建和编辑功能，因为文件上传需要特殊处理
  features: {
    create: false,
    edit: false,
    delete: true,
    batchDelete: true,
    search: true,
  },

  // 消息配置
  confirmMessages: {
    delete: '确定要删除这个文件吗？删除后无法恢复！',
    batchDelete: '确定要删除选中的文件吗？此操作不可撤销！',
  },

  successMessages: {
    delete: '文件删除成功！',
    batchDelete: '批量删除完成！',
  },
}

// 获取文件图标
function getFileIcon(fileType: string) {
  const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
  const videoTypes = ['mp4', 'avi', 'mov', 'wmv', 'flv']
  const audioTypes = ['mp3', 'wav', 'flac', 'aac']
  const archiveTypes = ['zip', 'rar', '7z', 'tar', 'gz']

  if (imageTypes.includes(fileType.toLowerCase())) {
    return Image
  }
  else if (videoTypes.includes(fileType.toLowerCase())) {
    return Video
  }
  else if (audioTypes.includes(fileType.toLowerCase())) {
    return Music
  }
  else if (archiveTypes.includes(fileType.toLowerCase())) {
    return Archive
  }
  else {
    return FileText
  }
}



// 下载文件
function downloadFile(file: UploadFile) {
  const link = document.createElement('a')
  link.href = file.url
  link.download = file.originalName
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 预览文件
function previewFile(file: UploadFile) {
  window.open(file.url, '_blank')
}


</script>

<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-semibold text-gray-900">
          文件管理
        </h1>
        <p class="mt-2 text-sm text-gray-700">
          管理系统中的所有上传文件，包括查看、分类、删除等操作。
        </p>
      </div>
    </div>

    <!-- 使用 EasyCurd 组件 -->
    <EasyCurd :config="config">
      <!-- 自定义文件信息列渲染 -->
      <template #cell-filename="{ value, row }">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <component
              :is="getFileIcon(row.fileType)"
              class="w-8 h-8 text-gray-400"
            />
          </div>
          <div class="ml-4">
            <div class="text-sm font-medium text-gray-900">
              {{ row.originalName }}
            </div>
            <div class="text-sm text-gray-500">
              {{ value }}
            </div>
            <div class="flex items-center gap-2 mt-1">
              <Badge :variant="getFileTypeBadgeVariant(row.fileType)">
                {{ row.fileType.toUpperCase() }}
              </Badge>
              <span class="text-xs text-gray-400">
                {{ formatFileSize(row.fileSize) }}
              </span>
            </div>
          </div>
        </div>
      </template>

      <!-- 自定义上传用户列渲染 -->
      <template #cell-user="{ row }">
        <div
          v-if="row.user"
          class="flex items-center"
        >
          <Avatar class="h-8 w-8 mr-3">
            <AvatarImage :src="row.user.avatar" />
            <AvatarFallback>{{ (row.user.name || '').charAt(0).toUpperCase() }}</AvatarFallback>
          </Avatar>
          <div>
            <div class="text-sm font-medium text-gray-900">
              {{ row.user.name }}
            </div>
            <div class="text-sm text-gray-500">
              {{ row.user.email }}
            </div>
          </div>
        </div>
        <div
          v-else
          class="text-sm text-gray-400"
        >
          未知用户
        </div>
      </template>

      <!-- 自定义上传时间列渲染 -->
      <template #cell-createdAt="{ value }">
        <div class="text-sm text-gray-500">
          {{ formatDate(value) }}
        </div>
      </template>

      <!-- 自定义操作列 -->
      <template #actions="{ row }">
        <div class="flex items-center justify-end gap-1">
          <Button
            variant="ghost"
            size="sm"
            @click="previewFile(row)"
          >
            <Eye class="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            @click="downloadFile(row)"
          >
            <Download class="w-4 h-4" />
          </Button>
        </div>
      </template>
    </EasyCurd>
  </div>
</template>
