// API 创建函数
export { createCosyApi } from './api'

// 字段组件
export * from './components/fields'
export { default as CurdFormCombobox } from './components/fields/Combobox.vue'
export { default as EasyCurd } from './components/StdCurd.vue'
// 核心组件
export { default as CurdForm } from './components/StdForm.vue'
export { default as CurdFormField } from './components/StdFormField.vue'
export { default as CurdPagination } from './components/StdPagination.vue'

export { default as CurdTable } from './components/StdTable.vue'

// Composables
export { useCurd } from './composables/useCurd'
export { useCurdFormValidation } from './composables/useCurdFormValidation'
export { useFieldLinkage } from './composables/useFieldLinkage'
export { useFormField } from './composables/useFormField'
export { useOptimizedCurd } from './composables/useOptimizedCurd'

// 核心类型定义
export * from './types'

export * from './utils/error-handler'

// 字段复用检测器（内部使用）
export * from './utils/field-reuse-detector'

export * from './utils/i18n'

// 工具函数
export * from './utils/performance'

export * from './utils/test-helpers'

// 核心：重新导出 TanStack Table 的完整功能
export * from '@tanstack/vue-table'
