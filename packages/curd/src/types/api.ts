import type { ColumnDef } from '@tanstack/vue-table'

// 分页类型
export interface Pagination {
  current_page: number
  per_page: number
  total_pages: number
  total: number
}

// API 响应类型
export interface ListResponse<T> {
  data: T[]
  pagination: Pagination
}

// 直接使用 TanStack Table 的 ColumnDef
export type StdColumn<T = any> = ColumnDef<T>

// CURD API 接口定义
export interface CurdApi<T = any> {
  getList: (params?: ListParams) => Promise<ListResponse<T>>
  getItem: (id: string | number) => Promise<T>
  createItem: (data: Partial<T>) => Promise<T>
  updateItem: (id: string | number, data: Partial<T>) => Promise<T>
  deleteItem: (id: string | number, query?: Record<string, any>) => Promise<T>
  restoreItem: (id: string | number) => Promise<T>
}

// 列表查询参数
export interface ListParams {
  page?: number
  per_page?: number
  sort_field?: string
  sort_order?: 'asc' | 'desc'
  [key: string]: any
}

// 扩展的列配置，支持排序
export type ExtendedColumn<T = any> = StdColumn<T> & {
  order?: number // 列的排序权重，数字越小越靠前
}
