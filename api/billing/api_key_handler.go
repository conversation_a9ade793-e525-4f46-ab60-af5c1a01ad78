package billing

import (
	"errors"
	"net/http"

	"git.uozi.org/uozi/potato-billing-api/internal/billing"
	"git.uozi.org/uozi/potato-billing-api/model"
	"git.uozi.org/uozi/potato-billing-api/query"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/logger"
)

// KeyStatusResponse Key状态响应结构
type KeyStatusResponse struct {
	ApiKey    string                      `json:"api_key"`
	Status    string                      `json:"status"`
	Available bool                        `json:"available"`
	Quotas    []*model.QuotaPackageRecord `json:"quotas"`
	User      *model.User                 `json:"user,omitempty"`
	CreatedAt int64                       `json:"created_at"`
	UpdatedAt int64                       `json:"updated_at"`
}

// KeyOverviewStats API Key概览统计响应结构
type KeyOverviewStats struct {
	TotalKeys       int64        `json:"total_keys"`        // 总Key数量
	ActiveKeys      int64        `json:"active_keys"`       // 活跃Key数量
	BlockedKeys     int64        `json:"blocked_keys"`      // 阻止Key数量
	KeysWithQuota   int64        `json:"keys_with_quota"`   // 有配额的Key数量
	KeysWithBalance int64        `json:"keys_with_balance"` // 用户有余额的Key数量
	TotalQuotaUsage float64      `json:"total_quota_usage"` // 总配额使用率
	AvgQuotaUsage   float64      `json:"avg_quota_usage"`   // 平均配额使用率
	ModuleStats     []ModuleStat `json:"module_stats"`      // 按模块统计
}

// ModuleStat 模块统计结构
type ModuleStat struct {
	Module      string  `json:"module"`       // 模块名称
	Name        string  `json:"name"`         // 模块显示名称
	KeyCount    int64   `json:"key_count"`    // Key数量
	ActiveCount int64   `json:"active_count"` // 活跃Key数量
	AvgUsage    float64 `json:"avg_usage"`    // 平均使用率
}

// InitKeyCurdAPI 初始化API密钥管理接口
func InitKeyCurdAPI(g *gin.RouterGroup) {
	g.GET("/keys", GetKeyList)
	g.GET("/keys/:key", GetKey)
	g.PUT("/keys/:key", UpdateKey)
	g.DELETE("/keys/:key", DeleteKey)
	g.POST("/keys", CreateKey)
	g.GET("/keys/overview/stats", GetKeyOverviewStats)
}

// GetKeyList 获取Key列表
func GetKeyList(c *gin.Context) {
	core := cosy.Core[model.ApiKey](c)

	core.SetPreloads("User")

	core.SetEqual("user_id")

	core.PagingList()
}

// CreateKey 创建Key
func CreateKey(c *gin.Context) {
	core := cosy.Core[model.ApiKey](c).SetValidRules(gin.H{
		"name":    "required",
		"user_id": "required",
		"status":  "omitempty",
		"comment": "omitempty",
	})

	core.BeforeExecuteHook(func(ctx *cosy.Ctx[model.ApiKey]) {
		ctx.Model.APIKey = uuid.New().String()
	})

	core.Create()
}

// UpdateKeyRequest 更新Key请求结构
type UpdateKeyRequest struct {
	Name    string `json:"name"`
	Comment string `json:"comment"`
	Status  string `json:"status"`
}

// GetKey 获取Key
func GetKey(c *gin.Context) {
	key := c.Param("key")
	if key == "" {
		cosy.ErrHandler(c, errors.New("Key参数必需"))
		return
	}

	apiKey, err := query.ApiKey.
		Preload(query.ApiKey.User).
		Where(query.ApiKey.APIKey.Eq(key)).
		First()
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, apiKey)
}

// UpdateKey 更新Key
func UpdateKey(c *gin.Context) {
	key := c.Param("key")
	if key == "" {
		cosy.ErrHandler(c, errors.New("Key参数必需"))
		return
	}

	body := &UpdateKeyRequest{}
	if err := c.ShouldBindJSON(body); err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	query.ApiKey.
		Where(query.ApiKey.APIKey.Eq(key)).
		Updates(body)

	GetKey(c)
}

// DeleteKey 删除Key
func DeleteKey(c *gin.Context) {
	key := c.Param("key")
	if key == "" {
		cosy.ErrHandler(c, errors.New("Key参数必需"))
		return
	}

	query.ApiKey.
		Where(query.ApiKey.APIKey.Eq(key)).
		Delete()

	c.JSON(http.StatusOK, gin.H{"message": "Key deleted successfully"})
}

// GetKeyStatus 获取Key状态
func GetKeyStatus(c *gin.Context) {
	key := c.Param("key")
	if key == "" {
		cosy.ErrHandler(c, errors.New("Key参数必需"))
		return
	}

	// 查询Key记录
	apiKey, err := query.ApiKey.
		Preload(query.ApiKey.User).
		Where(query.ApiKey.APIKey.Eq(key)).
		First()
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 查询该类型的专属配额信息
	var quotas []*model.QuotaPackageRecord
	err = query.QuotaPackageRecord.UnderlyingDB().
		Where("(api_key IN ? OR api_key IS NULL)", []string{key, ""}).
		Where("user_id = ?", apiKey.UserID).
		Find(&quotas).Error
	if err != nil {
		logger.Error("Failed to query quotas", "error", err, "key_id", apiKey.ID)
		cosy.ErrHandler(c, err)
		return
	}

	// 计算是否可用（只检查对应类型的配额）
	available := apiKey.Status == "ok"
	if len(quotas) > 0 {
		for _, quota := range quotas {
			if quota.Quota > 0 && quota.Used >= quota.Quota && apiKey.User.Balance <= 0 {
				available = false
				break
			}
		}
	} else {
		// 没有该类型的配额，检查用户余额
		if apiKey.User == nil || apiKey.User.Balance <= 0 {
			available = false
		}
	}

	response := KeyStatusResponse{
		ApiKey:    apiKey.APIKey,
		Status:    apiKey.Status,
		Available: available,
		Quotas:    quotas,
		User:      apiKey.User,
		CreatedAt: apiKey.CreatedAt,
		UpdatedAt: apiKey.UpdatedAt,
	}

	c.JSON(http.StatusOK, response)
}

// GetKeyOverviewStats 获取API Key概览统计
func GetKeyOverviewStats(c *gin.Context) {
	// 获取统计服务实例
	statService := billing.GetStatServiceInstance()
	if statService == nil {
		logger.Error("获取统计服务失败")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "统计服务不可用"})
		return
	}

	// 调用统计服务获取数据
	stats, err := statService.GetKeyOverviewStats(c.Request.Context())
	if err != nil {
		logger.Error("获取Key概览统计失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取统计数据失败"})
		return
	}

	c.JSON(http.StatusOK, stats)
}
